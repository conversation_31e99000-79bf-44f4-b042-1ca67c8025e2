import 'package:flutter/material.dart';
import 'package:kind_ali/core/utils/responsive_helper.dart';
import 'package:kind_ali/core/utils/extensions.dart';

/// A responsive image widget that adapts to different screen sizes
class ResponsiveImageWidget extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;
  final double? widthPercentage;
  final double? heightPercentage;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Color? color;
  final BlendMode? colorBlendMode;
  final bool maintainAspectRatio;
  final double aspectRatio;

  const ResponsiveImageWidget({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.widthPercentage,
    this.heightPercentage,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.color,
    this.colorBlendMode,
    this.maintainAspectRatio = true,
    this.aspectRatio = 1.0,
  });

  /// Factory constructor for responsive width
  factory ResponsiveImageWidget.width({
    required String imagePath,
    required double widthPercentage,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Color? color,
    BlendMode? colorBlendMode,
    double aspectRatio = 1.0,
  }) {
    return ResponsiveImageWidget(
      imagePath: imagePath,
      widthPercentage: widthPercentage,
      fit: fit,
      borderRadius: borderRadius,
      color: color,
      colorBlendMode: colorBlendMode,
      aspectRatio: aspectRatio,
    );
  }

  /// Factory constructor for responsive height
  factory ResponsiveImageWidget.height({
    required String imagePath,
    required double heightPercentage,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Color? color,
    BlendMode? colorBlendMode,
    double aspectRatio = 1.0,
  }) {
    return ResponsiveImageWidget(
      imagePath: imagePath,
      heightPercentage: heightPercentage,
      fit: fit,
      borderRadius: borderRadius,
      color: color,
      colorBlendMode: colorBlendMode,
      aspectRatio: aspectRatio,
    );
  }

  /// Factory constructor for square responsive image
  factory ResponsiveImageWidget.square({
    required String imagePath,
    required double sizePercentage,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Color? color,
    BlendMode? colorBlendMode,
  }) {
    return ResponsiveImageWidget(
      imagePath: imagePath,
      widthPercentage: sizePercentage,
      heightPercentage: sizePercentage,
      fit: fit,
      borderRadius: borderRadius,
      color: color,
      colorBlendMode: colorBlendMode,
      aspectRatio: 1.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Calculate responsive dimensions
    double? finalWidth;
    double? finalHeight;

    if (widthPercentage != null) {
      finalWidth = context.responsiveWidth(widthPercentage!);
    } else if (width != null) {
      finalWidth = ResponsiveHelper.getResponsiveWidth(context, width! / context.screenWidth);
    }

    if (heightPercentage != null) {
      finalHeight = context.responsiveHeight(heightPercentage!);
    } else if (height != null) {
      finalHeight = ResponsiveHelper.getResponsiveHeight(context, height! / context.screenHeight);
    }

    // Maintain aspect ratio if only one dimension is specified
    if (maintainAspectRatio) {
      if (finalWidth != null && finalHeight == null) {
        finalHeight = finalWidth / aspectRatio;
      } else if (finalHeight != null && finalWidth == null) {
        finalWidth = finalHeight * aspectRatio;
      }
    }

    Widget imageWidget;

    // Check if it's a network image or asset image
    if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
      imageWidget = Image.network(
        imagePath,
        width: finalWidth,
        height: finalHeight,
        fit: fit,
        color: color,
        colorBlendMode: colorBlendMode,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: finalWidth,
            height: finalHeight,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: borderRadius,
            ),
            child: Icon(
              Icons.error_outline,
              size: ResponsiveHelper.getResponsiveIconSize(context, 24),
              color: Colors.grey[600],
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: finalWidth,
            height: finalHeight,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
      );
    } else {
      imageWidget = Image.asset(
        imagePath,
        width: finalWidth,
        height: finalHeight,
        fit: fit,
        color: color,
        colorBlendMode: colorBlendMode,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: finalWidth,
            height: finalHeight,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: borderRadius,
            ),
            child: Icon(
              Icons.image_not_supported_outlined,
              size: ResponsiveHelper.getResponsiveIconSize(context, 24),
              color: Colors.grey[600],
            ),
          );
        },
      );
    }

    // Apply border radius if specified
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

/// Extension for easier responsive image creation
extension ResponsiveImageExtension on String {
  /// Create a responsive image widget from this string path
  ResponsiveImageWidget toResponsiveImage({
    double? widthPercentage,
    double? heightPercentage,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Color? color,
    BlendMode? colorBlendMode,
    double aspectRatio = 1.0,
  }) {
    return ResponsiveImageWidget(
      imagePath: this,
      widthPercentage: widthPercentage,
      heightPercentage: heightPercentage,
      fit: fit,
      borderRadius: borderRadius,
      color: color,
      colorBlendMode: colorBlendMode,
      aspectRatio: aspectRatio,
    );
  }
}
