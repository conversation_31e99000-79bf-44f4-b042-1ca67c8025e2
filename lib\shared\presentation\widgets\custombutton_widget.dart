
import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_dimensions.dart';
import 'package:kind_ali/core/utils/extensions.dart';

class CustombuttonWidget extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final double borderRadius;
  final double? height; // Made nullable to allow responsive default
  final double? width;
  final EdgeInsetsGeometry padding;
  final bool isFullWidth;
  final bool isLoading;
  final Widget? icon;
  final double elevation;
  final BorderSide borderSide;
  final TextStyle? textStyle;

  const CustombuttonWidget({
    Key? key,
    required this.onPressed,
    required this.text,
    this.backgroundColor = AppColors.accent,
    this.textColor = Colors.white,
    this.borderRadius = 8.0,
    this.height, // Will use responsive default if null
    this.width,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.isFullWidth = false,
    this.isLoading = false,
    this.icon,
    this.elevation = 2.0,
    this.borderSide = BorderSide.none,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use responsive height if not provided
    final responsiveHeight = height ?? AppDimensions.buttonHeightMedium(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(borderRadius),
        splashColor: Colors.white.withAlpha(50),
        highlightColor: Colors.white.withAlpha(30),
        child: Ink(
          width: isFullWidth ? double.infinity : width,
          height: responsiveHeight,
          padding: padding,
          decoration: BoxDecoration(
            color: isLoading ? backgroundColor.withAlpha(180) : backgroundColor,
            borderRadius: BorderRadius.circular(borderRadius),
            border: borderSide != BorderSide.none ? Border.fromBorderSide(borderSide) : null,
            boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha((elevation * 10).toInt()),
                    blurRadius: elevation * 2,
                    offset: Offset(0, elevation),
                  ),
                ]
              : null,
          ),
          child: Center(
            child: isLoading
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(textColor),
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (icon != null) ...[
                      icon!,
                      const SizedBox(width: 8),
                    ],
                    Text(
                      text,
                      style: textStyle ??
                          TextStyle(
                            color: textColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
          ),
        ),
      ),
    );
  }
}